package com.facishare.crm.fmcg.mengniu.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.api.*;
import com.facishare.crm.fmcg.mengniu.business.MengNiuObjectActionService;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileImageProcessRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StreamUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class AgreementService implements IAgreementService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;
    @Resource
    private TenantHierarchyService tenantHierarchyService;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private StoneProxyApi stoneProxyApi;
    @Resource
    private MengNiuObjectActionService mengNiuObjectActionService;

    private static final String AGREEMENT_API_NAME = "ActivityAgreement__c";

    private static final String STORE_ID_FIELD = "field_n6t3d__c";

    private static final String CONFIRMED_STATUS_FIELD = "field_b81hq__c";
    private static final String UNCONFIRMED = "2";
    private static final String CONFIRMED = "1";
    private static final String CONFIRMED_KEY = "__confirmed";

    private static final String AGREEMENT_STATUS_FIELD = "checkState__c";
    private static final String ON_INVALID_APPROVE = "6";
    private static final String INVALID = "7";
    private static final String APPROVE = "2";

    private static final String AGREEMENT_CONFIRM_TIME = "agreement_confirm_time__c";

    public static final String SIGNATURE_FIELD = "field_hTHe7__c";

    public static final String COST_APPROVE_API_NAME = "CostApprove__c";
    public static final String COST_APPROVE_ACCOUNT_ID_FIELD = "field_n90J1__c";

    protected static final Map<String, String> DETAIL_API_NAMES = Maps.newHashMap();
    protected static final Map<String, String> ITEM_SORT_API_NAME = Maps.newHashMap();

    private Cache<String, Map<String, Integer>> itemOrderCache;

    private List<String> activityAgreementMasterFields = Lists.newArrayList();
    private List<String> activityAgreementDetailFields = Lists.newArrayList();
    private String financialUserRoleId = "";
    private String cancelAgreementButtonName = "";

    static {
        DETAIL_API_NAMES.put("ActivityAgreementDetail__c", "field_0TBz2__c");
        ITEM_SORT_API_NAME.put("ActivityAgreementDetail__c", "field_26zAP__c");
    }

    @PostConstruct
    public void init() {
        itemOrderCache = CacheBuilder.newBuilder()
                .maximumSize(2000)
                .expireAfterWrite(120, TimeUnit.MINUTES)
                .build();

        ConfigFactory.getConfig("fs-fmcg-framework-config", conf -> {
            String downstreamAgreementFields = conf.get("meng-niu-downstream-agreement-fields");
            String downstreamAgreementDetailFields = conf.get("meng-niu-downstream-agreement-detail-fields");
            financialUserRoleId = conf.get("meng-niu-downstream-agreement-financial-role-id");
            cancelAgreementButtonName = conf.get("meng-niu-downstream-agreement-cancel-agreement-button-name");
            if (!Strings.isNullOrEmpty(downstreamAgreementFields)) {
                activityAgreementMasterFields = Arrays.stream(downstreamAgreementFields.split(",")).collect(Collectors.toList());
            }
            if (!Strings.isNullOrEmpty(downstreamAgreementDetailFields)) {
                activityAgreementDetailFields = Arrays.stream(downstreamAgreementDetailFields.split(",")).collect(Collectors.toList());
            }
        });

    }

    @Override
    public TriggerAgreementApproval.Result triggerAgreementApproval(TriggerAgreementApproval.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        log.info("context : {}", context);

        long total = 0;
        for (String tenantId : arg.getTenantIds()) {
            List<IObjectData> data = Lists.newArrayList();
            try {
                data = findNoConfirmTimeAgreements(tenantId);
            } catch (Exception ex) {
                log.info(String.format("find data failed : %s - %s", tenantId, ex.getMessage()), ex);
            }

            for (IObjectData datum : data) {

                try {
                    datum.set(AGREEMENT_CONFIRM_TIME, System.currentTimeMillis());
                    TriggerAction.Arg triggerArg = TriggerAction.Arg.builder()
                            .tenantId(tenantId)
                            .actionName("Edit")
                            .apiName(datum.getDescribeApiName())
                            .objectData(datum)
                            .triggerFlow(true)
                            .triggerWorkflow(true)
                            .skipFunctionAction(true)
                            .skipValidationFunctionCheck(true)
                            .skipValidationRuleCheck(true)
                            .skipBaseValidate(true)
                            .notValidate(true)
                            .build();

                    mengNiuObjectActionService.triggerAction(triggerArg).getObjectData().toObjectData();

                    log.info("trigger approval success : {} - {}", datum.getTenantId(), datum.getName());

                    total = total + 1;
                } catch (Exception ex) {
                    log.info(String.format("trigger approval failed : %s - %s", datum.getTenantId(), datum.getName()), ex);
                }
            }
        }
        return TriggerAgreementApproval.Result.builder().total(total).build();
    }

    @Override
    public FixAgreement.Result fixAgreement(FixAgreement.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String apiName = arg.getApiName();
        if (Strings.isNullOrEmpty(apiName)){
            apiName = ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ;
        }

        log.info("context : {}", context);

        long total = 0;
        for (String tenantId : arg.getTenantIds()) {
            List<IObjectData> data = Lists.newArrayList();

            try {
                data = findErrorAgreements(tenantId, apiName);
            } catch (Exception ex) {
                log.info(String.format("find data failed : %s - %s", tenantId, ex.getMessage()), ex);
            }

            for (IObjectData datum : data) {
                try {
                    TriggerAction.Arg triggerArg = TriggerAction.Arg.builder()
                            .tenantId(tenantId)
                            .actionName("Edit")
                            .apiName(datum.getDescribeApiName())
                            .objectData(datum)
                            .triggerFlow(true)
                            .triggerWorkflow(true)
                            .skipFunctionAction(true)
                            .skipValidationFunctionCheck(true)
                            .skipValidationRuleCheck(true)
                            .skipBaseValidate(true)
                            .notValidate(true)
                            .build();

                    mengNiuObjectActionService.triggerAction(triggerArg).getObjectData().toObjectData();

                    log.info("fix agreement success : {} - {}", datum.getTenantId(), datum.getName());

                    total = total + 1;
                } catch (Exception ex) {
                    log.info(String.format("fix agreement failed : %s - %s", datum.getTenantId(), datum.getName()), ex);
                }
            }
        }
        return FixAgreement.Result.builder().total(total).build();
    }

    @Override
    public AgreementList.Result list(AgreementList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        log.info("context : {}", context);

        if (Strings.isNullOrEmpty(context.getOutTenantId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_0));
        }

        if (notStoreOwner(context.getTenantId(), context.getOutTenantId(), context.getOutUserId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_1));
        }

        MengNiuTenantInformation tenant = tenantHierarchyService.load(context.getTenantId());
        String agreementTenantId = findAgreementTenantId(tenant);
        if (Strings.isNullOrEmpty(agreementTenantId)) {
            return AgreementList.Result.builder().build();
        }

        String storeId = findStoreId(context.getTenantId(), context.getOutTenantId());
        log.info("store id : {}", storeId);

        if (Strings.isNullOrEmpty(storeId)) {
            return AgreementList.Result.builder().data(Lists.newArrayList()).build();
        }

        List<IObjectData> data = findStoreAgreements(agreementTenantId, storeId, arg.getType());
        log.info("agreements size : {}", data.size());

        IObjectDescribe describe = serviceFacade.findObject(agreementTenantId, AGREEMENT_API_NAME);

        fillWebDetailData(agreementTenantId, describe, data);

        return AgreementList.Result.builder()
                .data(data.stream().map(ObjectDataDocument::of).collect(Collectors.toList()))
                .describe(ObjectDescribeDocument.of(describe))
                .simpleLayout(loadSimpleLayout())
                .build();
    }

    @Override
    public AgreementDetail.Result detail(AgreementDetail.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        log.info("context : {}", context);

        if (Strings.isNullOrEmpty(context.getOutTenantId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_2));
        }

        if (notStoreOwner(context.getTenantId(), context.getOutTenantId(), context.getOutUserId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_3));
        }

        MengNiuTenantInformation tenant = tenantHierarchyService.load(context.getTenantId());
        String agreementTenantId = findAgreementTenantId(tenant);
        if (Strings.isNullOrEmpty(agreementTenantId)) {
            return AgreementDetail.Result.builder().build();
        }

        Map<String, ObjectDescribeDocument> describes = Maps.newHashMap();
        Map<String, List<ObjectDataDocument>> detailsMap = Maps.newHashMap();

        IObjectData data = serviceFacade.findObjectData(User.systemUser(agreementTenantId), arg.getId(), AGREEMENT_API_NAME);
        log.info("data : {}", data);

        IObjectDescribe describe = serviceFacade.getObjectDescribeService().findObject(agreementTenantId, AGREEMENT_API_NAME);
        describes.put(describe.getApiName(), ObjectDescribeDocument.of(describe));

        fillWebDetailData(agreementTenantId, describe, Lists.newArrayList(data));

        for (Map.Entry<String, String> entry : DETAIL_API_NAMES.entrySet()) {
            String detailApiName = entry.getKey();
            String masterDetailFieldApiName = entry.getValue();

            IObjectDescribe detailDescribe = serviceFacade.findObject(agreementTenantId, detailApiName);
            describes.put(detailDescribe.getApiName(), ObjectDescribeDocument.of(detailDescribe));

            List<IObjectData> details = queryDetails(agreementTenantId, detailApiName, masterDetailFieldApiName, arg.getId());
            log.info("details size : {}", details.size());

            fillWebDetailData(agreementTenantId, detailDescribe, details);

            if (ITEM_SORT_API_NAME.containsKey(detailApiName)) {
                details = orderByItem(agreementTenantId, details, ITEM_SORT_API_NAME.get(detailApiName));
            }

            List<ObjectDataDocument> detailDocuments = details.stream().map(ObjectDataDocument::of).collect(Collectors.toList());
            detailsMap.put(detailApiName, detailDocuments);
        }

        return AgreementDetail.Result.builder()
                .data(ObjectDataDocument.of(data))
                .details(detailsMap)
                .describes(describes)
                .simpleLayout(loadSimpleLayout())
                .build();
    }

    @Override
    public AgreementConfirm.Result confirm(AgreementConfirm.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        log.info("context : {}", context);

        if (Strings.isNullOrEmpty(context.getOutTenantId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_4));
        }

        if (notStoreOwner(context.getTenantId(), context.getOutTenantId(), context.getOutUserId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_5));
        }

        MengNiuTenantInformation tenant = tenantHierarchyService.load(context.getTenantId());
        String agreementTenantId = findAgreementTenantId(tenant);
        if (Strings.isNullOrEmpty(agreementTenantId)) {
            return AgreementConfirm.Result.builder().build();
        }

        IObjectData agreement = serviceFacade.findObjectData(User.systemUser(agreementTenantId), arg.getId(), AGREEMENT_API_NAME);

        String confirmedStatus = agreement.get(CONFIRMED_STATUS_FIELD, String.class);
        if (CONFIRMED.equals(confirmedStatus)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_6));
        }

        String agreementStatus = agreement.get(AGREEMENT_STATUS_FIELD, String.class);
        if (INVALID.equals(agreementStatus) || ON_INVALID_APPROVE.equals(agreementStatus)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_AGREEMENT_SERVICE_7));
        }

        String signaturePath = convertSignature(context.getTenantId(), agreementTenantId, arg.getSignature());

        IObjectDescribe describe = serviceFacade.findObject(agreementTenantId, AGREEMENT_API_NAME);
        agreement = doSave(agreementTenantId, agreement, describe, signaturePath);

        fillWebDetailData(agreementTenantId, describe, Lists.newArrayList(agreement));

        return AgreementConfirm.Result.builder().data(ObjectDataDocument.of(agreement)).build();
    }

    @Override
    public DownstreamMonthlyAgreementListHeader.Result downstreamMonthlyAgreementListHeader(DownstreamMonthlyAgreementListHeader.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String downstreamTenantId = findDownstreamTenantId(context.getTenantId(), arg.getId());

        IObjectDescribe describe = serviceFacade.findObject(downstreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        List<MengNiuHeaderVO> headers = Lists.newArrayList();
        Map<String, IFieldDescribe> fields = describe.getFieldDescribeMap();

        for (String activityAgreementMasterField : activityAgreementMasterFields) {
            IFieldDescribe field = fields.get(activityAgreementMasterField);
            if (Objects.isNull(field)) {
                continue;
            }
            List<MengNiuOptionVO> options = Lists.newArrayList();
            if ("select_one".equals(field.getType()) || "select_many".equals(field.getType())) {
                @SuppressWarnings("unchecked")
                List<Map<String, String>> originalOptions = (List<Map<String, String>>) field.get("options");
                if (CollectionUtils.isNotEmpty(originalOptions)) {
                    options.addAll(originalOptions.stream().map(this::mapToMengNiuOption).collect(Collectors.toList()));
                }
            }
            headers.add(MengNiuHeaderVO.builder().key(field.getApiName()).label(field.getLabel()).type(field.getType()).options(options).hide(false).build());
        }
        return DownstreamMonthlyAgreementListHeader.Result.builder()
                .enableCancelAgreementButton(isFinancialUser(context.getTenantId(), context.getEmployeeId().toString()))
                .cancelAgreementButtonName(this.cancelAgreementButtonName)
                .headerList(headers)
                .build();
    }

    @Override
    public DownstreamMonthlyAgreementDetailListHeader.Result downstreamMonthlyAgreementDetailListHeader(DownstreamMonthlyAgreementDetailListHeader.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String downstreamTenantId = findDownstreamTenantId(context.getTenantId(), arg.getId());

        IObjectDescribe describe = serviceFacade.findObject(downstreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ);

        List<MengNiuHeaderVO> headers = Lists.newArrayList();
        Map<String, IFieldDescribe> fields = describe.getFieldDescribeMap();

        for (String activityAgreementDetailField : activityAgreementDetailFields) {
            IFieldDescribe field = fields.get(activityAgreementDetailField);
            if (Objects.isNull(field)) {
                continue;
            }
            List<MengNiuOptionVO> options = Lists.newArrayList();
            if ("select_one".equals(field.getType()) || "select_many".equals(field.getType())) {
                @SuppressWarnings("unchecked")
                List<Map<String, String>> originalOptions = (List<Map<String, String>>) field.get("options");
                if (CollectionUtils.isNotEmpty(originalOptions)) {
                    options.addAll(originalOptions.stream().map(this::mapToMengNiuOption).collect(Collectors.toList()));
                }
            }
            headers.add(MengNiuHeaderVO.builder().key(field.getApiName()).label(field.getLabel()).type(field.getType()).options(options).hide(false).build());
        }
        return DownstreamMonthlyAgreementDetailListHeader.Result.builder().headerList(headers).build();
    }

    @Override
    public DownstreamMonthlyAgreementList.Result downstreamMonthlyAgreementList(DownstreamMonthlyAgreementList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String downStreamTenantId = findDownstreamTenantId(context.getTenantId(), arg.getId());

        QueryResult<IObjectData> agreementDetailResult = queryDownstreamMonthlyAgreements(downStreamTenantId, arg.getId(), arg.getOffset(), arg.getLimit());
        List<IObjectData> agreements = agreementDetailResult.getData();

        if (CollectionUtils.isNotEmpty(agreements)) {
            IObjectDescribe describe = serviceFacade.findObject(downStreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            fillData(downStreamTenantId, describe, agreements);
        }

        return DownstreamMonthlyAgreementList.Result
                .builder()
                .data(agreements.stream().map(ObjectDataDocument::of).collect(Collectors.toList()))
                .total(agreementDetailResult.getTotalNumber())
                .build();
    }

    @Override
    public DownstreamMonthlyAgreementDetailList.Result downstreamMonthlyAgreementDetailList(DownstreamMonthlyAgreementDetailList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String downStreamTenantId = findDownstreamTenantId(context.getTenantId(), arg.getId());

        List<IObjectData> agreementDetails = queryDownstreamMonthlyAgreementDetails(downStreamTenantId, arg.getActivityAgreementId());

        if (CollectionUtils.isNotEmpty(agreementDetails)) {
            IObjectDescribe describe = serviceFacade.findObject(downStreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ);
            fillData(downStreamTenantId, describe, agreementDetails);
        }

        return DownstreamMonthlyAgreementDetailList.Result
                .builder()
                .data(agreementDetails.stream().map(ObjectDataDocument::of).collect(Collectors.toList()))
                .build();
    }

    @Override
    public DownstreamCancelAgreement.Result downstreamCancelAgreement(DownstreamCancelAgreement.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        if (!isFinancialUser(context.getTenantId(), context.getEmployeeId().toString())) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_SERVICE_0));
        }

        String downStreamTenantId = findDownstreamTenantId(context.getTenantId(), arg.getId());
        if (CollectionUtils.isEmpty(arg.getActivityAgreementIdList())) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_SERVICE_1));
        }

        List<IObjectData> agreements = serviceFacade.findObjectDataByIds(downStreamTenantId, arg.getActivityAgreementIdList(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        for (IObjectData agreement : agreements) {
            agreement.set("field_80Od3__c", false);
        }
        List<IObjectData> canceledAgreements = serviceFacade.batchUpdate(agreements, User.systemUser(downStreamTenantId));
        IObjectDescribe describe = serviceFacade.findObject(downStreamTenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        fillData(downStreamTenantId, describe, canceledAgreements);

        return DownstreamCancelAgreement.Result.builder().data(canceledAgreements.stream().map(ObjectDataDocument::of).collect(Collectors.toList())).build();
    }

    private boolean isFinancialUser(String tenantId, String userId) {
        List<String> roles = serviceFacade.getUserRole(User.builder().tenantId(tenantId).userId(userId).build());
        return roles.contains(financialUserRoleId);
    }

    private String findAgreementTenantId(MengNiuTenantInformation tenant) {
        switch (tenant.getRole()) {
            case MengNiuTenantInformation.ROLE_N:
                return tenant.getN().getTenantId();
            case MengNiuTenantInformation.ROLE_M:
                if (TPMGrayUtils.enableMengNiuMAgreement(tenant.getN().getTenantId())) {
                    return tenant.getM().getTenantId();
                } else {
                    return tenant.getN().getTenantId();
                }
            default:
                return "";
        }
    }

    private IObjectData doSave(String tenantId, IObjectData agreement, IObjectDescribe describe, String signaturePath) {
        Map<String, Object> signatureImage = Maps.newHashMap();
        signatureImage.put("ext", "jpg");
        signatureImage.put("path", signaturePath);

        agreement.set(SIGNATURE_FIELD, Lists.newArrayList(signatureImage));
        agreement.set(CONFIRMED_STATUS_FIELD, CONFIRMED);
        agreement.set(AGREEMENT_STATUS_FIELD, APPROVE);

        agreement = serviceFacade.updateObjectData(User.systemUser(tenantId), agreement);

        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.AGREEMENT_STORE_CONFIRM, describe, agreement, "门店确认协议");//ignorei18n

        log.info("final object data : {}", JSON.toJSONString(agreement));

        agreement.set(AGREEMENT_CONFIRM_TIME, System.currentTimeMillis());

        TriggerAction.Arg arg = TriggerAction.Arg.builder()
                .tenantId(tenantId)
                .actionName("Edit")
                .apiName(agreement.getDescribeApiName())
                .objectData(agreement)
                .triggerFlow(true)
                .triggerWorkflow(true)
                .skipFunctionAction(true)
                .skipValidationFunctionCheck(true)
                .skipValidationRuleCheck(true)
                .skipBaseValidate(true)
                .notValidate(true)
                .build();

        return mengNiuObjectActionService.triggerAction(arg).getObjectData().toObjectData();
    }

    private String convertSignature(String tenantId, String targetTenantId, String signature) {
        if (tenantId.equals(targetTenantId)) {
            return signature;
        }

        String tenantAccount = enterpriseEditionService.getEnterpriseAccount(Integer.parseInt(tenantId));

        StoneFileDownloadRequest downloadRequest = new StoneFileDownloadRequest();
        downloadRequest.setPath(signature);
        downloadRequest.setEa(tenantAccount);
        downloadRequest.setEmployeeId(-10000);
        downloadRequest.setBusiness("FMCG-TPM");

        InputStream stream;
        try {
            stream = stoneProxyApi.downloadStream(downloadRequest);
        } catch (FRestClientException ex) {
            throw new MetaDataBusinessException("download image cause io exception", ex);
        }

        String targetTenantAccount = enterpriseEditionService.getEnterpriseAccount(Integer.parseInt(targetTenantId));

        byte[] bytes;
        try {
            bytes = StreamUtils.copyToByteArray(stream);
        } catch (IOException e) {
            throw new MetaDataBusinessException("read signature error.");
        }

        StoneFileUploadRequest uploadRequest = new StoneFileUploadRequest();
        uploadRequest.setImageProcessRequest(new StoneFileImageProcessRequest());
        uploadRequest.setNeedThumbnail(false);
        uploadRequest.setFileSize(bytes.length);
        uploadRequest.setKeepFormat(true);
        uploadRequest.setSecurityGroup("");
        uploadRequest.setPermissions(Lists.newArrayList());
        uploadRequest.setGlobal(false);
        uploadRequest.setExtensionName(".jpg");
        uploadRequest.setNamedPath("");
        uploadRequest.setEa(targetTenantAccount);
        uploadRequest.setEmployeeId(-10000);
        uploadRequest.setBusiness("FMCG-TPM");

        InputStream uploadStream = new ByteArrayInputStream(bytes);
        try {
            StoneFileUploadResponse uploadResponse = stoneProxyApi.uploadByStream("n", uploadRequest, uploadStream);
            return uploadResponse.getPath();
        } catch (FRestClientException ex) {
            log.error("upload by stream error : ", ex);
            throw new MetaDataBusinessException("convert signature error.");
        }
    }

    private List<IObjectData> orderByItem(String tenantId, List<IObjectData> details, String fieldApiName) {
        Map<String, Integer> order = loadItemOrder(tenantId);
        if (MapUtils.isEmpty(order)) {
            return details;
        }

        for (IObjectData detail : details) {
            String key = detail.get(fieldApiName, String.class);
            if (!Strings.isNullOrEmpty(key) && order.containsKey(key)) {
                detail.set("__order", order.get(key));
            } else {
                detail.set("__order", Integer.MAX_VALUE);
            }
        }

        return details.stream().sorted(Comparator.comparing(detail -> detail.get("__order", Integer.class))).collect(Collectors.toList());
    }

    private Map<String, Integer> loadItemOrder(String tenantId) {
        Map<String, Integer> order = itemOrderCache.getIfPresent(tenantId);
        if (Objects.isNull(order)) {
            order = loadItemOrderFromPG(tenantId);
            itemOrderCache.put(tenantId, order);
        }
        return order;
    }

    private Map<String, Integer> loadItemOrderFromPG(String tenantId) {
        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "TPMActivityItemObj", QueryDataUtil.minimumQuery(), Lists.newArrayList("sort__c", CommonFields.ID));
        Map<String, Integer> order = Maps.newHashMap();
        for (IObjectData datum : data) {
            String id = datum.getId();
            Integer index = datum.get("sort__c", Integer.class);
            if (!Strings.isNullOrEmpty(id) && Objects.nonNull(index)) {
                order.put(id, index);
            }
        }
        return order;
    }

    private boolean notStoreOwner(String tenantId, String storeId, String outUserId) {
        return false;
    }

    private List<IObjectData> queryDetails(String tenantId, String detailApiName, String masterDetailFieldApiName, String id) {
        IFilter masterFilter = new Filter();
        masterFilter.setFieldName(masterDetailFieldApiName);
        masterFilter.setOperator(Operator.EQ);
        masterFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(masterFilter);

        return QueryDataUtil.find(serviceFacade, tenantId, detailApiName, stq);
    }

    private List<IObjectData> findErrorAgreements(String tenantId, String apiName) {
        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__INEFFECTIVE));

        IFilter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(CommonFields.IS_DELETED);
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("0"));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(isDeletedFilter, lifeStatusFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq);
    }

    private List<IObjectData> findNoConfirmTimeAgreements(String tenantId) {
        IFilter agreementStatusFilter = new Filter();
        agreementStatusFilter.setFieldName(AGREEMENT_STATUS_FIELD);
        agreementStatusFilter.setOperator(Operator.NIN);
        agreementStatusFilter.setFieldValues(Lists.newArrayList(ON_INVALID_APPROVE, INVALID));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        IFilter confirmedFilter = new Filter();
        confirmedFilter.setFieldName(CONFIRMED_STATUS_FIELD);
        confirmedFilter.setOperator(Operator.EQ);
        confirmedFilter.setFieldValues(Lists.newArrayList(CONFIRMED));

        IFilter confirmTimeFilter = new Filter();
        confirmTimeFilter.setFieldName(AGREEMENT_CONFIRM_TIME);
        confirmTimeFilter.setOperator(Operator.IS);
        confirmTimeFilter.setFieldValues(Lists.newArrayList());

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(false);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(confirmTimeFilter, confirmedFilter, agreementStatusFilter, lifeStatusFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(serviceFacade, tenantId, AGREEMENT_API_NAME, stq);
    }

    private List<IObjectData> findStoreAgreements(String tenantId, String storeId, String type) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, Calendar.JANUARY);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.YEAR, -1);

        long year = cal.getTimeInMillis();

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName(STORE_ID_FIELD);
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        IFilter timeFilter = new Filter();
        timeFilter.setFieldName(CommonFields.CREATE_TIME);
        timeFilter.setOperator(Operator.GT);
        timeFilter.setFieldValues(Lists.newArrayList(String.valueOf(year)));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        IFilter agreementStatusFilter = new Filter();
        agreementStatusFilter.setFieldName(AGREEMENT_STATUS_FIELD);
        agreementStatusFilter.setOperator(Operator.NIN);
        agreementStatusFilter.setFieldValues(Lists.newArrayList(ON_INVALID_APPROVE, INVALID));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.LAST_MODIFY_TIME);
        order.setIsAsc(false);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(storeIdFilter, timeFilter, agreementStatusFilter, lifeStatusFilter),
                Lists.newArrayList(order)
        );

        switch (type) {
            case "UNCONFIRMED":
                IFilter unconfirmedFilter = new Filter();
                unconfirmedFilter.setFieldName(CONFIRMED_STATUS_FIELD);
                unconfirmedFilter.setOperator(Operator.EQ);
                unconfirmedFilter.setFieldValues(Lists.newArrayList(UNCONFIRMED));
                stq.getFilters().add(unconfirmedFilter);
                break;
            case "CONFIRMED":
                IFilter confirmedFilter = new Filter();
                confirmedFilter.setFieldName(CONFIRMED_STATUS_FIELD);
                confirmedFilter.setOperator(Operator.EQ);
                confirmedFilter.setFieldValues(Lists.newArrayList(CONFIRMED));
                stq.getFilters().add(confirmedFilter);
                break;
            default:
                break;
        }

        return QueryDataUtil.find(serviceFacade, tenantId, AGREEMENT_API_NAME, stq);
    }

    private JSONObject loadSimpleLayout() {
        String json;
        try {
            File file = ResourceUtils.getFile("classpath:tpm/module_mengniu_agreement/simple_layout.json");
            json = new String(Files.readAllBytes(file.toPath()));
        } catch (IOException ex) {
            throw new MetaDataBusinessException("simple layout can not found.");
        }
        return JSON.parseObject(json);
    }

    private String findStoreId(String tenantId, String outerTenantId) {
        IFilter enterpriseAccountFilter = new Filter();
        enterpriseAccountFilter.setFieldName("dest_outer_tenant_id");
        enterpriseAccountFilter.setOperator(Operator.EQ);
        enterpriseAccountFilter.setFieldValues(Lists.newArrayList(outerTenantId));

        IFilter relationTypeFilter = new Filter();
        relationTypeFilter.setFieldName("relation_type");
        relationTypeFilter.setOperator(Operator.EQ);
        relationTypeFilter.setFieldValues(Lists.newArrayList("1"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(enterpriseAccountFilter, relationTypeFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "EnterpriseRelationObj", stq, Lists.newArrayList(
                "_id", "mapper_account_id"
        ));

        if (CollectionUtils.isEmpty(data)) {
            return null;
        }

        return data.get(0).get("mapper_account_id", String.class);
    }

    private void fillWebDetailData(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        infraServiceFacade.fillQuoteFieldValue(User.systemUser(tenantId), data, describe, null, false);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        serviceFacade.fillUserInfo(describe, data, User.systemUser(tenantId));
        serviceFacade.fillDepartmentInfo(describe, data, User.systemUser(tenantId));

        for (IObjectData datum : data) {
            String confirmedStatus = datum.get(CONFIRMED_STATUS_FIELD, String.class);
            Long confirmTime = datum.get(AGREEMENT_CONFIRM_TIME, Long.class);
            datum.set(CONFIRMED_KEY, CONFIRMED.equals(confirmedStatus) && !Objects.isNull(confirmTime));
        }
    }

    private void fillData(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        infraServiceFacade.fillQuoteFieldValue(User.systemUser(tenantId), data, describe, null, false);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        serviceFacade.fillUserInfo(describe, data, User.systemUser(tenantId));
        serviceFacade.fillDepartmentInfo(describe, data, User.systemUser(tenantId));
    }

    private MengNiuOptionVO mapToMengNiuOption(Map<String, String> map) {
        String value = map.getOrDefault("value", null);
        String label = map.getOrDefault("label", null);
        String fontColor = map.getOrDefault("font_color", null);
        return MengNiuOptionVO.builder().label(label).value(value).fontColor(fontColor).build();
    }

    private String findDownstreamTenantId(String tenantId, String id) {
        IObjectData approval = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), id, COST_APPROVE_API_NAME);
        if (Objects.isNull(approval)) {
            throw new ValidateException("approval data not found.");
        }

        String accountId = approval.get(COST_APPROVE_ACCOUNT_ID_FIELD, String.class, "");
        if (Strings.isNullOrEmpty(accountId)) {
            throw new ValidateException("approval data not found.");
        }

        IFilter mapperAccountIdFilter = new Filter();
        mapperAccountIdFilter.setFieldName(EnterpriseRelationFields.MAPPER_ACCOUNT_ID);
        mapperAccountIdFilter.setOperator(Operator.EQ);
        mapperAccountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(mapperAccountIdFilter);
        stq.setLimit(1);
        stq.setOffset(0);

        List<IObjectData> relation = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.ENTERPRISE_RELATION_OBJ, stq, Lists.newArrayList(
                CommonFields.ID, EnterpriseRelationFields.ENTERPRISE_ACCOUNT
        ));

        if (CollectionUtils.isEmpty(relation)) {
            throw new ValidateException("downstream enterprise not found.");
        }

        String tenantAccount = relation.get(0).get(EnterpriseRelationFields.ENTERPRISE_ACCOUNT, String.class);
        if (Strings.isNullOrEmpty(tenantAccount)) {
            throw new ValidateException("downstream enterprise not found.");
        }

        return String.valueOf(enterpriseEditionService.getEnterpriseId(tenantAccount));
    }

    private QueryResult<IObjectData> queryDownstreamMonthlyAgreements(String tenantId, String activityId, int offset, int limit) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        IFilter isDeleteFilter = new Filter();
        isDeleteFilter.setFieldName(CommonFields.IS_DELETED);
        isDeleteFilter.setOperator(Operator.EQ);
        isDeleteFilter.setFieldValues(Lists.newArrayList(Boolean.FALSE.toString()));

        IFilter isSubmitFilter = new Filter();
        isSubmitFilter.setFieldName("is_submint__c");
        isSubmitFilter.setOperator(Operator.EQ);
        isSubmitFilter.setFieldValues(Lists.newArrayList("2"));

        IFilter feeFilter = new Filter();
        feeFilter.setFieldName("field_80Od3__c");
        feeFilter.setOperator(Operator.EQ);
        feeFilter.setFieldValues(Lists.newArrayList("true"));

        IFilter approvalStatusFilter = new Filter();
        approvalStatusFilter.setFieldName("field_Gvb1m__c");
        approvalStatusFilter.setOperator(Operator.NEQ);
        approvalStatusFilter.setFieldValues(Lists.newArrayList("7"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(activityIdFilter, lifeStatusFilter, isDeleteFilter, isSubmitFilter, feeFilter, approvalStatusFilter);
        stq.setNeedReturnCountNum(Boolean.TRUE);
        stq.setLimit(limit);
        stq.setOffset(offset);

        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, stq);
    }

    private List<IObjectData> queryDownstreamMonthlyAgreementDetails(String tenantId, String masterId) {
        IFilter relationTypeFilter = new Filter();
        relationTypeFilter.setFieldName(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID);
        relationTypeFilter.setOperator(Operator.EQ);
        relationTypeFilter.setFieldValues(Lists.newArrayList(masterId));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(CommonFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__NORMAL));

        IFilter isDeleteFilter = new Filter();
        isDeleteFilter.setFieldName(CommonFields.IS_DELETED);
        isDeleteFilter.setOperator(Operator.EQ);
        isDeleteFilter.setFieldValues(Lists.newArrayList(Boolean.FALSE.toString()));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(relationTypeFilter, lifeStatusFilter, isDeleteFilter);

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_AGREEMENT_DETAIL_OBJ, stq);
    }
}
