package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ITPMFieldService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2023/8/11 11:34
 */

@Service
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMFieldServiceImpl implements ITPMFieldService {
    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public String loadFieldDescribeJsonFromResource(String objectApiName, String fieldApiName) {
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_fields/%s.%s.json", objectApiName, fieldApiName));
            return new String(Files.readAllBytes(file.toPath()));
        } catch (IOException e) {
            throw new MetaDataBusinessException("read field describe from file cause io exception.");
        }
    }

    @Override
    public JSONObject loadFieldToJsonByPath(String path) {
        String json;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/%s.json", path));
            json = new String(Files.readAllBytes(file.toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("activity type template resource file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read activity type template resource failed.");
        }
        return JSON.parseObject(json);
    }

    @Override
    public Layout loadLayout(String layoutApiName) {
        String json;
        try {
            File file = ResourceUtils.getFile(String.format("classpath:tpm/module_activity_type_template/layout.%s.json", layoutApiName));
            json = new String(Files.readAllBytes(file.toPath()));
        } catch (FileNotFoundException e) {
            throw new MetaDataBusinessException("activity type template resource file not found.");
        } catch (IOException ex) {
            throw new MetaDataBusinessException("read activity type template resource failed.");
        }
        return new Layout(JSON.parseObject(json));
    }

    @Override
    public void addField(String tenantId, IObjectDescribe describe, String fieldDescribe, boolean isShow, Boolean isReadOnly) {
        User superUser = User.systemUser(tenantId);

        JSONObject field = JSON.parseObject(fieldDescribe);

        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(superUser, ActivityTypeTemplateService.DETAIL_LAYOUT_TYPE, describe.getApiName());

        fieldLayout.setApiName(layout.getName());
        fieldLayout.setLabel(field.getString("label"));
        fieldLayout.setRenderType(field.getString("type"));
        fieldLayout.setReadonly(isReadOnly == null ? Boolean.TRUE.equals(field.getBoolean("is_readonly")) : isReadOnly);
        fieldLayout.setRequired(field.getBoolean("is_required"));
        fieldLayout.setShow(isShow);
        fieldLayout.setLayoutType(ActivityTypeTemplateService.DETAIL_LAYOUT_TYPE);

        LanguageReplaceWrapper.doInChinese(() -> {
            serviceFacade.addDescribeCustomField(superUser,
                    describe.getApiName(),
                    fieldDescribe,
                    Lists.newArrayList(fieldLayout),
                    Lists.newArrayList());
        });
    }

    @Override
    public boolean containsFiled(IObjectDescribe describe, String fieldApiName) {
        if (describe == null || fieldApiName == null) {
            return false;
        }
        List<String> fields = describe.getFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        return fields.contains(fieldApiName);
    }
}
