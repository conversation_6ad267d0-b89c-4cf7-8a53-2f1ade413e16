package com.facishare.crm.fmcg.tpm.web.designer;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.common.utils.LanguageReplaceWrapper;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofAuditModeConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofAuditSourceConfigVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/30 12:14
 */
//IgnoreI18nFile
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@Component("activityProofAuditNodeHandler")
public class ActivityProofAuditNodeHandler extends BaseSystemNodeHandler {

    private static final String AUDIT_STATUS_API_NAME = "audit_status__c";

    private static final Logger LOG = LoggerFactory.getLogger(ActivityProofAuditNodeHandler.class);

    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;

    @Override
    public String getNodeType() {
        return NodeType.AUDIT.value();
    }

    @Override
    public void validation(String tenantId, int index, List<ActivityNodeVO> nodes, IActivityType activityType) {
        super.validation(tenantId, index, nodes, activityType);
        ActivityNodeVO node = nodes.get(index);

        auditSourceConfigValidation(tenantId, node.getActivityProofAuditConfig().getAuditSourceConfig(), nodes);
        auditModeConfigValidation(node.getActivityProofAuditConfig().getAuditModeConfig());
    }

    private void auditSourceConfigValidation(String tenantId, ActivityProofAuditSourceConfigVO config, List<ActivityNodeVO> nodes) {
        if (Objects.isNull(config)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_0));
        }

        ActivityNodeVO sourceNode = nodes.stream().filter(f -> f.getTemplateId().equals(config.getTemplateId())).findFirst().orElse(null);
        if (sourceNode == null) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_1));
        }
        config.setMasterApiName(sourceNode.getObjectApiName());
        config.setMasterRecordType(sourceNode.getObjectRecordType());
        config.setReferenceActivityFieldApiName(sourceNode.getReferenceActivityFieldApiName());
        if (Strings.isNullOrEmpty(config.getAuditStatusApiName())) {
            if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(config.getMasterApiName())) {
                config.setAuditStatusApiName(TPMActivityProofFields.AUDIT_STATUS);
            } else {
                IObjectDescribe describe = describeService.findObject(tenantId, config.getMasterApiName());
                if (!describe.getFieldDescribeMap().containsKey(AUDIT_STATUS_API_NAME)) {
                    LanguageReplaceWrapper.doInChinese(() -> {
                        describeService.addDescribeCustomField(User.systemUser(tenantId), config.getMasterApiName(), loadAuditFieldString(), fromLayoutSection(tenantId, config.getMasterApiName()), Lists.newArrayList());
                    });
                }
                config.setAuditStatusApiName(AUDIT_STATUS_API_NAME);
            }
        }

        if (Boolean.TRUE.equals(config.getUseComplexMode())) {

            if (ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(config.getMasterApiName())) {
                config.setDealerFieldApiName(TPMActivityProofFields.DEALER_ID);
                config.setAccountFieldApiName(TPMActivityProofFields.STORE_ID);
                config.setReferenceAuditSourceFieldApiName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
                config.setCostFieldApiName(TPMActivityProofFields.ACTUAL_TOTAL);
            } else {
                if (Strings.isNullOrEmpty(config.getMasterApiName())) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_2));
                }
                IObjectDescribe masterDescribe = describeService.findObject(tenantId, config.getMasterApiName());
                if (Objects.isNull(masterDescribe)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_3));
                }

                auditSourceReferenceFieldValidation(config.getDealerFieldApiName(), ApiNames.ACCOUNT_OBJ, "查找关联经销商", false, masterDescribe);
                auditSourceReferenceFieldValidation(config.getAgreementFieldApiName(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, "查找关联协议", false, masterDescribe);
                auditSourceReferenceFieldValidation(config.getAccountFieldApiName(), ApiNames.ACCOUNT_OBJ, "查找关联客户", true, masterDescribe);
                for (String displayFieldApiNameOfMaster : config.getDisplayFieldApiNamesOfMaster()) {
                    auditSourceFieldValidation(displayFieldApiNameOfMaster, masterDescribe);
                }
                IObjectDescribe auditDescribe = describeService.findObject(tenantId, ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
                auditSourceReferenceFieldValidation(config.getReferenceAuditSourceFieldApiName(), config.getMasterApiName(), "查找关联被检核对象", true, auditDescribe);
            }

            if (!Strings.isNullOrEmpty(config.getDetailApiName())) {
                IObjectDescribe detailDescribe = describeService.findObject(tenantId, config.getDetailApiName());
                ObjectDescribeExt detailDescribeExt = ObjectDescribeExt.of(detailDescribe);
                MasterDetailFieldDescribe masterDetailFieldDescribe = detailDescribeExt.getMasterDetailFieldDescribe().orElse(null);
                if (Objects.isNull(masterDetailFieldDescribe)) {
                    throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_4));
                }
                config.setMasterDetailFieldApiName(masterDetailFieldDescribe.getApiName());
            }
        } else if (Boolean.FALSE.equals(config.getUseComplexMode())) {
            if (!ApiNames.TPM_ACTIVITY_PROOF_OBJ.equals(config.getMasterApiName())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_5));
            }

            config.setDealerFieldApiName("dealer_id");
            config.setAccountFieldApiName("store_id");
            config.setReferenceAuditSourceFieldApiName("activity_proof_id");
            config.setReferenceAuditSourceDetailFieldApiName("activity_proof_detail_id");

        } else {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_6));
        }
    }

    private void auditModeConfigValidation(ActivityProofAuditModeConfigVO config) {
        if (Strings.isNullOrEmpty(config.getAuditMode())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_7));
        }
    }

    private void auditSourceReferenceFieldValidation(String fieldApiName, String referenceTargetApiName, String displayName, boolean isRequired, IObjectDescribe masterDescribe) {
        if (!isRequired && Strings.isNullOrEmpty(fieldApiName)) {
            return;
        }
        if (Strings.isNullOrEmpty(fieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_8), displayName));
        }
        if (!masterDescribe.getFieldDescribeMap().containsKey(fieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_9), fieldApiName));
        }
        IFieldDescribe field = masterDescribe.getFieldDescribe(fieldApiName);
        if (!"object_reference".equals(field.getType())) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_10), displayName));
        }
        String targetApiName = FieldDescribeExt.of(field).getRefObjTargetApiName();
        if (Strings.isNullOrEmpty(targetApiName) || !referenceTargetApiName.equals(targetApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_11), displayName));
        }
    }

    private void auditSourceFieldValidation(String fieldApiName, IObjectDescribe masterDescribe) {
        if (!masterDescribe.getFieldDescribeMap().containsKey(fieldApiName)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_12), fieldApiName));
        }
    }

    @Override
    protected List<String> queryPostSystemNodeTypes() {
        return Lists.newArrayList(
                NodeType.AUDIT.value(),
                NodeType.STORE_WRITE_OFF.value(),
                NodeType.COST_ASSIGN.value(),
                NodeType.WRITE_OFF.value()
        );
    }

    private String loadAuditFieldString() {
        try {
            BufferedInputStream inputStream = new BufferedInputStream(this.getClass().getResourceAsStream("/constant/audit_status__c.json"));
            byte[] bytes = new byte[inputStream.available()];
            inputStream.read(bytes);
            inputStream.close();
            return new String(bytes, Charsets.UTF_8);
        } catch (IOException e) {
            LOG.info("read file err.", e);
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_AUDIT_NODE_HANDLER_13));
        }
    }

    private List<FieldLayoutPojo> fromLayoutSection(String tenantId, String apiName) {
        ILayout layout = serviceFacade.getLayoutLogicService().findDefaultLayout(User.systemUser(tenantId), "detail", apiName);
        FieldLayoutPojo pojo = new FieldLayoutPojo();
        pojo.setApiName(layout.get("api_name", String.class));
        pojo.setLabel(layout.getName());
        pojo.setLayoutType(layout.getLayoutType());
        pojo.setRenderType("select_one");
        pojo.setShow(true);
        pojo.setReadonly(true);
        pojo.setRequired(false);

        return Lists.newArrayList(pojo);
    }

}
