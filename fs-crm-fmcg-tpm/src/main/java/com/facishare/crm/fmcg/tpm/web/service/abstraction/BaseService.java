package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.open.app.center.api.service.QueryAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ValidateRuleService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/30 20:05
 */
@SuppressWarnings("Duplicates")
@Slf4j
public abstract class BaseService {

    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected InfraServiceFacade infraServiceFacade;
    @Resource
    private QueryAppAdminService queryAppAdminService;
    @Resource(name = "tpmRoleService")
    private IRoleService roleService;

    protected static final String APPSERVER_OPEN_PROXY_APPINFO_CONFIG_KEY = "fs-appserver-openproxy-appinfo";
    protected static final String TPM_APP_ID_CONFIG_KEY = "tpm";
    protected static final List<String> BUDGET_ROLE_CODE = Lists.newArrayList("00000000000000000000000000000006", "budgetFinance");

    protected void permissionCheckForBudget(ApiContext context) {
        rolePermissionCheck(context.getTenantId(), context.getEmployeeId(), BUDGET_ROLE_CODE);
    }

    private void rolePermissionCheck(String enterpriseId, int userId, List<String> roleIds) {
        if (userId == -10000) {
            return;
        }
        List<String> roleCodes = roleService.queryRoleCodeListByUserId(enterpriseId, userId);
        if (CollectionUtils.isEmpty(roleCodes)) {
            throw new ValidateException(I18N.text(I18NKeys.BASE_SERVICE_0));
        }
        if (!CollectionUtils.containsAny(roleCodes, roleIds)) {
            throw new ValidateException(I18N.text(I18NKeys.BASE_SERVICE_1));
        }
    }

    protected void permissionCheckForTPM(ApiContext context) {
        appPermissionCheck(context.getTenantId(), context.getEmployeeId(), TPM_APP_ID_CONFIG_KEY);
    }

    protected List<IObjectData> findObjectDataByIds(String tenantId, IObjectDescribe describe, List<String> ids, boolean needFillWebDetailData) {
        if (needFillWebDetailData) {
            return fillWebDetailData(tenantId, describe, serviceFacade.findObjectDataByIds(tenantId, ids, describe.getApiName()));
        } else {
            return serviceFacade.findObjectDataByIds(tenantId, ids, describe.getApiName());
        }
    }

    protected IObjectData fillWebDetailData(String tenantId, String apiName, IObjectData data) {
        IObjectDescribe describe = serviceFacade.getObjectDescribeService().findObject(tenantId, apiName);
        return fillWebDetailData(tenantId, describe, data);
    }

    protected IObjectData fillWebDetailData(String tenantId, IObjectDescribe describe, IObjectData data) {
        return fillWebDetailData(tenantId, describe, Lists.newArrayList(data)).get(0);
    }

    protected List<IObjectData> fillWebDetailData(String tenantId, IObjectDescribe describe, List<IObjectData> data) {
        infraServiceFacade.fillQuoteFieldValue(User.systemUser(tenantId), data, describe, null, false);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        serviceFacade.fillUserInfo(describe, data, User.systemUser(tenantId));
        serviceFacade.fillDepartmentInfo(describe, data, User.systemUser(tenantId));
        return data;
    }

    protected List<IObjectData> findDetailsByMasterObject(String tenantId, IObjectDescribe describe, IObjectData masterData) {
        List<IObjectData> applicationDataDetailList = serviceFacade.findDetailObjectDataList(describe, masterData, User.systemUser(tenantId));
        return fillWebDetailData(tenantId, describe, applicationDataDetailList);
    }

    private void appPermissionCheck(String tenantId, int userId, String configKey) {
        if (userId == -10000){
            return;
        }
        String tenantAccount = serviceFacade.getEAByEI(tenantId);
        String appId = ConfigFactory.getConfig(APPSERVER_OPEN_PROXY_APPINFO_CONFIG_KEY).get(configKey);
        FsUserVO user = FsUserVO.toFsUserVO(String.format("E.%s.%s", tenantAccount, userId));
        BaseResult<Boolean> result = queryAppAdminService.isAppAdmin(user, appId);
        if (Objects.isNull(result)) {
            throw new ValidateException(I18N.text(I18NKeys.BASE_SERVICE_2));
        }
        if (!result.isSuccess()) {
            throw new ValidateException(result.getErrMessage(), result.getErrCode());
        }
        if (!Boolean.TRUE.equals(result.getResult())) {
            throw new ValidateException(I18N.text(I18NKeys.BASE_SERVICE_3));
        }
    }

    public TPMAppPermission appPermission(String tenantAccount, int userId) {
        String appId = ConfigFactory.getConfig(APPSERVER_OPEN_PROXY_APPINFO_CONFIG_KEY).get(TPM_APP_ID_CONFIG_KEY);
        FsUserVO user = FsUserVO.toFsUserVO(String.format("E.%s.%s", tenantAccount, userId));
        BaseResult<Boolean> result = queryAppAdminService.isAppAdmin(user, appId);
        if (Objects.isNull(result)) {
            log.info("进行权限校验时发生未知异常， result is empty");
            return TPMAppPermission.builder().isTPMAppAdmin(false).errCode(2001).errMessage(I18N.text(I18NKeys.BASE_SERVICE_2)).build();
        }
        if (!result.isSuccess()) {
            return TPMAppPermission.builder().isTPMAppAdmin(false).errCode(result.getErrCode()).errMessage(result.getErrMessage()).build();
        }
        if (!Boolean.TRUE.equals(result.getResult())) {
            //"权限校验失败，您不是营销活动应用管理员或者营销活动主管。"
            return TPMAppPermission.builder().isTPMAppAdmin(false).errCode(2002).errMessage(I18N.text(I18NKeys.BASE_SERVICE_3)).build();
        }
        return TPMAppPermission.builder().isTPMAppAdmin(true).errCode(0).errMessage("").build();
    }

    @Data
    @Builder
    public static class TPMAppPermission {
        private boolean isTPMAppAdmin;
        private String errMessage;
        private Integer errCode;
    }
}
